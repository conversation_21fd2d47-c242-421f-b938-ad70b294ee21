'use client'

import { useState } from 'react'
import { AlertTriangle, CheckCircle, XCircle, RefreshCw } from 'lucide-react'

interface DiagnosticResult {
  test: string
  status: 'success' | 'error' | 'pending'
  message: string
  details?: any
}

export default function NetworkDiagnostics() {
  const [results, setResults] = useState<DiagnosticResult[]>([])
  const [isRunning, setIsRunning] = useState(false)

  const runDiagnostics = async () => {
    setIsRunning(true)
    const diagnostics: DiagnosticResult[] = []

    // Test 1: Basic connectivity
    try {
      const startTime = Date.now()
      const response = await fetch(window.location.origin, { 
        method: 'HEAD',
        cache: 'no-cache'
      })
      const duration = Date.now() - startTime
      
      diagnostics.push({
        test: 'Server Connectivity',
        status: response.ok ? 'success' : 'error',
        message: response.ok 
          ? `Server is reachable (${duration}ms)` 
          : `Server returned ${response.status}`,
        details: { status: response.status, duration }
      })
    } catch (error) {
      diagnostics.push({
        test: 'Server Connectivity',
        status: 'error',
        message: `Cannot reach server: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: error
      })
    }

    // Test 2: API endpoint availability
    try {
      const startTime = Date.now()
      const response = await fetch('/api/products', {
        method: 'HEAD',
        cache: 'no-cache'
      })
      const duration = Date.now() - startTime

      diagnostics.push({
        test: 'API Endpoint',
        status: response.ok ? 'success' : 'error',
        message: response.ok 
          ? `API endpoint is available (${duration}ms)` 
          : `API returned ${response.status}: ${response.statusText}`,
        details: { status: response.status, statusText: response.statusText, duration }
      })
    } catch (error) {
      diagnostics.push({
        test: 'API Endpoint',
        status: 'error',
        message: `API endpoint failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: error
      })
    }

    // Test 3: Full API request
    try {
      const startTime = Date.now()
      const response = await fetch('/api/products', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        cache: 'no-cache'
      })
      const duration = Date.now() - startTime

      if (response.ok) {
        const data = await response.json()
        diagnostics.push({
          test: 'Full API Request',
          status: 'success',
          message: `API request successful (${duration}ms)`,
          details: { 
            status: response.status, 
            duration,
            dataReceived: !!data,
            hasProducts: data?.data?.products?.length || 0
          }
        })
      } else {
        diagnostics.push({
          test: 'Full API Request',
          status: 'error',
          message: `API request failed: ${response.status} ${response.statusText}`,
          details: { status: response.status, statusText: response.statusText, duration }
        })
      }
    } catch (error) {
      diagnostics.push({
        test: 'Full API Request',
        status: 'error',
        message: `API request error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: error
      })
    }

    // Test 4: Browser environment checks
    const browserChecks = {
      fetch: typeof fetch !== 'undefined',
      AbortController: typeof AbortController !== 'undefined',
      location: typeof window !== 'undefined' && !!window.location,
      origin: typeof window !== 'undefined' ? window.location.origin : 'N/A'
    }

    diagnostics.push({
      test: 'Browser Environment',
      status: browserChecks.fetch && browserChecks.AbortController ? 'success' : 'error',
      message: 'Browser API availability check',
      details: browserChecks
    })

    setResults(diagnostics)
    setIsRunning(false)
  }

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />
      case 'pending':
        return <RefreshCw className="w-5 h-5 text-yellow-500 animate-spin" />
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <AlertTriangle className="w-5 h-5 text-orange-500" />
          <h3 className="text-lg font-semibold">Network Diagnostics</h3>
        </div>
        <button
          onClick={runDiagnostics}
          disabled={isRunning}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 flex items-center gap-2"
        >
          {isRunning ? (
            <>
              <RefreshCw className="w-4 h-4 animate-spin" />
              Running...
            </>
          ) : (
            <>
              <RefreshCw className="w-4 h-4" />
              Run Diagnostics
            </>
          )}
        </button>
      </div>

      {results.length > 0 && (
        <div className="space-y-3">
          {results.map((result, index) => (
            <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
              {getStatusIcon(result.status)}
              <div className="flex-1">
                <div className="font-medium">{result.test}</div>
                <div className="text-sm text-gray-600">{result.message}</div>
                {result.details && (
                  <details className="mt-2">
                    <summary className="text-xs text-gray-500 cursor-pointer">Show details</summary>
                    <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                      {JSON.stringify(result.details, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {results.length === 0 && !isRunning && (
        <div className="text-center text-gray-500 py-8">
          Click "Run Diagnostics" to test your network connection and API endpoints
        </div>
      )}
    </div>
  )
}

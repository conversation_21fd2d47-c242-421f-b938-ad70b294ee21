// Performance monitoring utility for tracking API response times and errors

import { logger } from '@/lib/logger'

interface PerformanceMetric {
  endpoint: string
  method: string
  startTime: number
  endTime?: number
  duration?: number
  success?: boolean
  error?: string
  retryCount?: number
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private maxMetrics = 100 // Keep only last 100 metrics

  startTracking(endpoint: string, method: string = 'GET'): string {
    const id = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const metric: PerformanceMetric = {
      endpoint,
      method,
      startTime: Date.now()
    }
    
    this.metrics.push(metric)
    
    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics)
    }
    
    return id
  }

  endTracking(endpoint: string, success: boolean, error?: string, retryCount?: number) {
    const metric = this.metrics.find(m => 
      m.endpoint === endpoint && 
      !m.endTime && 
      Date.now() - m.startTime < 60000 // Within last minute
    )
    
    if (metric) {
      metric.endTime = Date.now()
      metric.duration = metric.endTime - metric.startTime
      metric.success = success
      metric.error = error || ''
      metric.retryCount = retryCount || 0
    }
  }

  getMetrics(endpoint?: string): PerformanceMetric[] {
    if (endpoint) {
      return this.metrics.filter(m => m.endpoint === endpoint && m.endTime)
    }
    return this.metrics.filter(m => m.endTime)
  }

  getAverageResponseTime(endpoint?: string): number {
    const metrics = this.getMetrics(endpoint)
    if (metrics.length === 0) return 0
    
    const totalDuration = metrics.reduce((sum, m) => sum + (m.duration || 0), 0)
    return totalDuration / metrics.length
  }

  getSuccessRate(endpoint?: string): number {
    const metrics = this.getMetrics(endpoint)
    if (metrics.length === 0) return 0
    
    const successCount = metrics.filter(m => m.success).length
    return (successCount / metrics.length) * 100
  }

  getRecentErrors(endpoint?: string, minutes: number = 5): PerformanceMetric[] {
    const cutoff = Date.now() - (minutes * 60 * 1000)
    const metrics = this.getMetrics(endpoint)
    
    return metrics.filter(m => 
      !m.success && 
      m.endTime && 
      m.endTime > cutoff
    )
  }

  generateReport(): string {
    const allMetrics = this.getMetrics()
    const recentMetrics = allMetrics.filter(m => 
      m.endTime && m.endTime > Date.now() - (5 * 60 * 1000) // Last 5 minutes
    )

    const report = `
Performance Report (Last 5 minutes):
=====================================
Total Requests: ${recentMetrics.length}
Average Response Time: ${Math.round(this.getAverageResponseTime())}ms
Success Rate: ${Math.round(this.getSuccessRate())}%

Recent Errors:
${this.getRecentErrors().map(m => 
  `- ${m.endpoint}: ${m.error} (${m.duration}ms)`
).join('\n') || 'None'}

Slowest Requests:
${recentMetrics
  .sort((a, b) => (b.duration || 0) - (a.duration || 0))
  .slice(0, 5)
  .map(m => `- ${m.endpoint}: ${m.duration}ms`)
  .join('\n') || 'None'}
    `.trim()

    return report
  }

  clear() {
    this.metrics = []
  }
}

// Global instance
export const performanceMonitor = new PerformanceMonitor()

// Helper function to wrap fetch with performance tracking and enhanced error handling
export async function trackedFetch(
  endpoint: string,
  options?: RequestInit,
  retryCount: number = 0
): Promise<Response> {
  performanceMonitor.startTracking(endpoint, options?.method || 'GET')

  try {
    console.log('🔍 trackedFetch: Starting request', { endpoint, method: options?.method || 'GET', retryCount })

    // Try a simple fetch first to see if the basic functionality works
    let response: Response

    try {
      console.log('🔍 trackedFetch: Preparing fetch options...')

      // Enhanced fetch options with better error handling
      const fetchOptions: RequestInit = {
        ...options,
        // Add default headers if not provided
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          ...options?.headers,
        },
        // Ensure we have a reasonable timeout
        signal: options?.signal || AbortSignal.timeout(30000),
        // Add credentials for same-origin requests
        credentials: 'same-origin',
        // Add cache control
        cache: 'no-cache',
      }

      console.log('🔍 trackedFetch: Fetch options prepared:', fetchOptions)
      console.log('🔍 trackedFetch: About to call fetch with endpoint:', endpoint)
      console.log('🔍 trackedFetch: Current window.location:', window?.location?.href)
      console.log('🔍 trackedFetch: Fetch function available:', typeof fetch)

      if (typeof fetch === 'undefined') {
        throw new Error('Fetch is not available in this environment')
      }

      console.log('🔍 trackedFetch: Calling fetch now...')
      response = await fetch(endpoint, fetchOptions)
      console.log('🔍 trackedFetch: Enhanced fetch completed successfully')
    } catch (enhancedFetchError) {
      console.warn('🔍 trackedFetch: Enhanced fetch failed, error details:', enhancedFetchError)
      console.warn('🔍 trackedFetch: Enhanced fetch error type:', typeof enhancedFetchError)
      console.warn('🔍 trackedFetch: Enhanced fetch error constructor:', enhancedFetchError?.constructor?.name)

      try {
        console.log('🔍 trackedFetch: Trying basic fetch as fallback...')
        // Fallback to basic fetch
        response = await fetch(endpoint, {
          method: options?.method || 'GET',
          headers: options?.headers || {},
          body: options?.body,
          signal: options?.signal
        })
        console.log('🔍 trackedFetch: Basic fetch completed successfully')
      } catch (basicFetchError) {
        console.error('🔍 trackedFetch: Basic fetch also failed:', basicFetchError)
        throw basicFetchError
      }
    }

    console.log('🔍 trackedFetch: Response received', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
      headers: Object.fromEntries(response.headers.entries())
    })

    performanceMonitor.endTracking(endpoint, response.ok, response.ok ? undefined : `HTTP ${response.status}`, retryCount)
    return response
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    const errorName = error instanceof Error ? error.name : 'Unknown'
    const errorStack = error instanceof Error ? error.stack : 'No stack trace'

    console.error('🔍 trackedFetch: Error occurred', {
      endpoint,
      errorMessage,
      errorName,
      errorType: error?.constructor?.name,
      retryCount,
      fullError: error
    })

    console.error('🔍 trackedFetch: Error details:', error)
    console.error('🔍 trackedFetch: Error stack:', errorStack)

    performanceMonitor.endTracking(endpoint, false, errorMessage, retryCount)

    // Handle AbortErrors gracefully
    if (error instanceof Error && error.name === 'AbortError') {
      console.log('🔍 trackedFetch: Request was aborted (this is normal for canceled requests)')
      throw error // Re-throw AbortError as-is
    }

    // Enhanced error information for other errors
    if (error instanceof TypeError && error.message === 'Failed to fetch') {
      const enhancedError = new Error(`Network request failed for ${endpoint}. This could be due to:
1. Server not running (check if localhost:3000 is accessible)
2. CORS issues (check browser console for CORS errors)
3. Network connectivity problems
4. Browser security policies blocking the request

Original error: ${error.message}`)
      enhancedError.name = 'NetworkError'
      throw enhancedError
    }

    throw error
  }
}

// Console logging helper
export function logPerformanceReport() {
  logger.info('Performance Report', { report: performanceMonitor.generateReport() })
}

// Auto-log performance report every 5 minutes in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  setInterval(() => {
    const metrics = performanceMonitor.getMetrics()
    if (metrics.length > 0) {
      logger.info('Performance Report scheduled')
      logPerformanceReport()
    }
  }, 5 * 60 * 1000) // Every 5 minutes
}
